{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "i18n:extract": "i18next 'src/**/*.{js,jsx,ts,tsx}' --output locales"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@monaco-editor/react": "^4.7.0", "@paypal/react-paypal-js": "^8.8.1", "@reduxjs/toolkit": "^2.5.1", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.4.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.66.0", "@tanstack/react-table": "^8.21.3", "@types/pusher-js": "^5.1.0", "@types/swiper": "^6.0.0", "axios": "^1.7.9", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^11.3.1", "i18next": "^24.1.0", "js-cookie": "^3.0.5", "laravel-echo": "^2.1.3", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.55.0", "react-i18next": "^15.2.0", "react-icons": "^5.5.0", "react-lazy-load-image-component": "^1.6.3", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-slick": "^0.30.3", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.3", "recharts": "^2.15.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-lazy-load-image-component": "^1.6.4", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "i18next-parser": "^9.3.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-plugin-compression": "^0.5.1"}}